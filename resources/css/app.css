@import './fonts.css' layer(base);
@import 'tailwindcss';
/*
  ---break---
*/
@custom-variant dark (&:is(.dark *));

@config '../../tailwind.config.js';

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything st
  ill
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {

    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentColor);
    }
  body {
    letter-spacing: var(--tracking-normal);
    }
}
@layer base {
    :root {
  --background: oklch(0.9777 0.0041 301.4256);
  --foreground: oklch(0.3651 0.0325 287.0807);
  --card: oklch(1.0000 0 0);
  --card-foreground: oklch(0.3651 0.0325 287.0807);
  --popover: oklch(1.0000 0 0);
  --popover-foreground: oklch(0.3651 0.0325 287.0807);
  --primary: oklch(0.6104 0.0767 299.7335);
  --primary-foreground: oklch(0.9777 0.0041 301.4256);
  --secondary: oklch(0.8957 0.0265 300.2416);
  --secondary-foreground: oklch(0.3651 0.0325 287.0807);
  --muted: oklch(0.8906 0.0139 299.7754);
  --muted-foreground: oklch(0.5288 0.0375 290.7895);
  --accent: oklch(0.7889 0.0802 359.9375);
  --accent-foreground: oklch(0.3394 0.0441 1.7583);
  --destructive: oklch(0.6332 0.1578 22.6734);
  --destructive-foreground: oklch(0.9777 0.0041 301.4256);
  --border: oklch(0.8447 0.0226 300.1421);
  --input: oklch(0.9329 0.0124 301.2783);
  --ring: oklch(0.6104 0.0767 299.7335);
  --chart-1: oklch(0.6104 0.0767 299.7335);
  --chart-2: oklch(0.7889 0.0802 359.9375);
  --chart-3: oklch(0.7321 0.0749 169.8670);
  --chart-4: oklch(0.8540 0.0882 76.8292);
  --chart-5: oklch(0.7857 0.0645 258.0839);
  --sidebar: oklch(0.9554 0.0082 301.3541);
  --sidebar-foreground: oklch(0.3651 0.0325 287.0807);
  --sidebar-primary: oklch(0.6104 0.0767 299.7335);
  --sidebar-primary-foreground: oklch(0.9777 0.0041 301.4256);
  --sidebar-accent: oklch(0.7889 0.0802 359.9375);
  --sidebar-accent-foreground: oklch(0.3394 0.0441 1.7583);
  --sidebar-border: oklch(0.8719 0.0198 302.1690);
  --sidebar-ring: oklch(0.6104 0.0767 299.7335);
  --font-sans: Geist, sans-serif;
  --font-serif: "Lora", Georgia, serif;
  --font-mono: "Fira Code", "Courier New", monospace;
  --radius: 0.5rem;
  --shadow-2xs: 1px 2px 5px 1px hsl(0 0% 0% / 0.03);
  --shadow-xs: 1px 2px 5px 1px hsl(0 0% 0% / 0.03);
  --shadow-sm: 1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 1px 2px 0px hsl(0 0% 0% / 0.06);
  --shadow: 1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 1px 2px 0px hsl(0 0% 0% / 0.06);
  --shadow-md: 1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 2px 4px 0px hsl(0 0% 0% / 0.06);
  --shadow-lg: 1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 4px 6px 0px hsl(0 0% 0% / 0.06);
  --shadow-xl: 1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 8px 10px 0px hsl(0 0% 0% / 0.06);
  --shadow-2xl: 1px 2px 5px 1px hsl(0 0% 0% / 0.15);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
}

.dark {
  --background: oklch(0.2166 0.0215 292.8474);
  --foreground: oklch(0.9053 0.0245 293.5570);
  --card: oklch(0.2544 0.0301 292.7315);
  --card-foreground: oklch(0.9053 0.0245 293.5570);
  --popover: oklch(0.2544 0.0301 292.7315);
  --popover-foreground: oklch(0.9053 0.0245 293.5570);
  --primary: oklch(0.7058 0.0777 302.0489);
  --primary-foreground: oklch(0.2166 0.0215 292.8474);
  --secondary: oklch(0.4604 0.0472 295.5578);
  --secondary-foreground: oklch(0.9053 0.0245 293.5570);
  --muted: oklch(0.2560 0.0320 294.8380);
  --muted-foreground: oklch(0.6974 0.0282 300.0614);
  --accent: oklch(0.3181 0.0321 308.6149);
  --accent-foreground: oklch(0.8391 0.0692 2.6681);
  --destructive: oklch(0.6875 0.1420 21.4566);
  --destructive-foreground: oklch(0.2166 0.0215 292.8474);
  --border: oklch(0.3063 0.0359 293.3367);
  --input: oklch(0.2847 0.0346 291.2726);
  --ring: oklch(0.7058 0.0777 302.0489);
  --chart-1: oklch(0.7058 0.0777 302.0489);
  --chart-2: oklch(0.8391 0.0692 2.6681);
  --chart-3: oklch(0.7321 0.0749 169.8670);
  --chart-4: oklch(0.8540 0.0882 76.8292);
  --chart-5: oklch(0.7857 0.0645 258.0839);
  --sidebar: oklch(0.1985 0.0200 293.6639);
  --sidebar-foreground: oklch(0.9053 0.0245 293.5570);
  --sidebar-primary: oklch(0.7058 0.0777 302.0489);
  --sidebar-primary-foreground: oklch(0.2166 0.0215 292.8474);
  --sidebar-accent: oklch(0.3181 0.0321 308.6149);
  --sidebar-accent-foreground: oklch(0.8391 0.0692 2.6681);
  --sidebar-border: oklch(0.2847 0.0346 291.2726);
  --sidebar-ring: oklch(0.7058 0.0777 302.0489);
  --font-sans: Geist, sans-serif;
  --font-serif: "Lora", Georgia, serif;
  --font-mono: "Fira Code", "Courier New", monospace;
  --radius: 0.5rem;
  --shadow-2xs: 1px 2px 5px 1px hsl(0 0% 0% / 0.03);
  --shadow-xs: 1px 2px 5px 1px hsl(0 0% 0% / 0.03);
  --shadow-sm: 1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 1px 2px 0px hsl(0 0% 0% / 0.06);
  --shadow: 1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 1px 2px 0px hsl(0 0% 0% / 0.06);
  --shadow-md: 1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 2px 4px 0px hsl(0 0% 0% / 0.06);
  --shadow-lg: 1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 4px 6px 0px hsl(0 0% 0% / 0.06);
  --shadow-xl: 1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 8px 10px 0px hsl(0 0% 0% / 0.06);
  --shadow-2xl: 1px 2px 5px 1px hsl(0 0% 0% / 0.15);
}
  }

@layer base {
    * {
        @apply border-border;
    }

    body {
        @apply bg-background text-foreground;
    }

    /* Ensure light mode is the default */
    html {
        color-scheme: light;
    }

    html.dark {
        color-scheme: dark;
    }
}

/*
  ---break---
*/

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: Geist, sans-serif;
  --font-mono: "Fira Code", "Courier New", monospace;
  --font-serif: "Lora", Georgia, serif;

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
  --radius: 0.5rem;
  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  --tracking-normal: var(--tracking-normal);
  --spacing: var(--spacing);
  --letter-spacing: var(--letter-spacing);
  --shadow-offset-y: var(--shadow-offset-y);
  --shadow-offset-x: var(--shadow-offset-x);
  --shadow-spread: var(--shadow-spread);
  --shadow-blur: var(--shadow-blur);
  --shadow-opacity: var(--shadow-opacity);
  --color-shadow-color: var(--shadow-color);
}

/*
  ---break---
*/

:root {
    --background: oklch(0.9777 0.0041 301.4256);
    --foreground: oklch(0.3651 0.0325 287.0807);
    --card: oklch(1.0000 0 0);
    --card-foreground: oklch(0.3651 0.0325 287.0807);
    --popover: oklch(1.0000 0 0);
    --popover-foreground: oklch(0.3651 0.0325 287.0807);
    --primary: oklch(0.6104 0.0767 299.7335);
    --primary-foreground: oklch(0.9777 0.0041 301.4256);
    --secondary: oklch(0.8957 0.0265 300.2416);
    --secondary-foreground: oklch(0.3651 0.0325 287.0807);
    --muted: oklch(0.8906 0.0139 299.7754);
    --muted-foreground: oklch(0.5288 0.0375 290.7895);
    --accent: oklch(0.7889 0.0802 359.9375);
    --accent-foreground: oklch(0.3394 0.0441 1.7583);
    --destructive: oklch(0.6332 0.1578 22.6734);
    --destructive-foreground: oklch(0.9777 0.0041 301.4256);
    --border: oklch(0.8447 0.0226 300.1421);
    --input: oklch(0.9329 0.0124 301.2783);
    --ring: oklch(0.6104 0.0767 299.7335);
    --chart-1: oklch(0.6104 0.0767 299.7335);
    --chart-2: oklch(0.7889 0.0802 359.9375);
    --chart-3: oklch(0.7321 0.0749 169.8670);
    --chart-4: oklch(0.8540 0.0882 76.8292);
    --chart-5: oklch(0.7857 0.0645 258.0839);
    --radius: 0.5rem;
    --sidebar: oklch(0.9554 0.0082 301.3541);
    --sidebar-foreground: oklch(0.3651 0.0325 287.0807);
    --sidebar-primary: oklch(0.6104 0.0767 299.7335);
    --sidebar-primary-foreground: oklch(0.9777 0.0041 301.4256);
    --sidebar-accent: oklch(0.7889 0.0802 359.9375);
    --sidebar-accent-foreground: oklch(0.3394 0.0441 1.7583);
    --sidebar-border: oklch(0.8719 0.0198 302.1690);
    --sidebar-ring: oklch(0.6104 0.0767 299.7335);
    --font-sans: Geist, sans-serif;
    --font-serif: "Lora", Georgia, serif;
    --font-mono: "Fira Code", "Courier New", monospace;
    --shadow-color: hsl(0 0% 0%);
    --shadow-opacity: 0.06;
    --shadow-blur: 5px;
    --shadow-spread: 1px;
    --shadow-offset-x: 1px;
    --shadow-offset-y: 2px;
    --letter-spacing: 0em;
    --spacing: 0.25rem;
    --shadow-2xs: 1px 2px 5px 1px hsl(0 0% 0% / 0.03);
    --shadow-xs: 1px 2px 5px 1px hsl(0 0% 0% / 0.03);
    --shadow-sm: 1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 1px 2px 0px hsl(0 0% 0% / 0.06);
    --shadow: 1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 1px 2px 0px hsl(0 0% 0% / 0.06);
    --shadow-md: 1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 2px 4px 0px hsl(0 0% 0% / 0.06);
    --shadow-lg: 1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 4px 6px 0px hsl(0 0% 0% / 0.06);
    --shadow-xl: 1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 8px 10px 0px hsl(0 0% 0% / 0.06);
    --shadow-2xl: 1px 2px 5px 1px hsl(0 0% 0% / 0.15);
    --tracking-normal: 0em;
}

/*
  ---break---
*/

.dark {
    --background: oklch(0.2166 0.0215 292.8474);
    --foreground: oklch(0.9053 0.0245 293.5570);
    --card: oklch(0.2544 0.0301 292.7315);
    --card-foreground: oklch(0.9053 0.0245 293.5570);
    --popover: oklch(0.2544 0.0301 292.7315);
    --popover-foreground: oklch(0.9053 0.0245 293.5570);
    --primary: oklch(0.7058 0.0777 302.0489);
    --primary-foreground: oklch(0.2166 0.0215 292.8474);
    --secondary: oklch(0.4604 0.0472 295.5578);
    --secondary-foreground: oklch(0.9053 0.0245 293.5570);
    --muted: oklch(0.2560 0.0320 294.8380);
    --muted-foreground: oklch(0.6974 0.0282 300.0614);
    --accent: oklch(0.3181 0.0321 308.6149);
    --accent-foreground: oklch(0.8391 0.0692 2.6681);
    --destructive: oklch(0.6875 0.1420 21.4566);
    --destructive-foreground: oklch(0.2166 0.0215 292.8474);
    --border: oklch(0.3063 0.0359 293.3367);
    --input: oklch(0.2847 0.0346 291.2726);
    --ring: oklch(0.7058 0.0777 302.0489);
    --chart-1: oklch(0.7058 0.0777 302.0489);
    --chart-2: oklch(0.8391 0.0692 2.6681);
    --chart-3: oklch(0.7321 0.0749 169.8670);
    --chart-4: oklch(0.8540 0.0882 76.8292);
    --chart-5: oklch(0.7857 0.0645 258.0839);
    --sidebar: oklch(0.1985 0.0200 293.6639);
    --sidebar-foreground: oklch(0.9053 0.0245 293.5570);
    --sidebar-primary: oklch(0.7058 0.0777 302.0489);
    --sidebar-primary-foreground: oklch(0.2166 0.0215 292.8474);
    --sidebar-accent: oklch(0.3181 0.0321 308.6149);
    --sidebar-accent-foreground: oklch(0.8391 0.0692 2.6681);
    --sidebar-border: oklch(0.2847 0.0346 291.2726);
    --sidebar-ring: oklch(0.7058 0.0777 302.0489);
    --radius: 0.5rem;
    --font-sans: Geist, sans-serif;
    --font-serif: "Lora", Georgia, serif;
    --font-mono: "Fira Code", "Courier New", monospace;
    --shadow-color: hsl(0 0% 0%);
    --shadow-opacity: 0.06;
    --shadow-blur: 5px;
    --shadow-spread: 1px;
    --shadow-offset-x: 1px;
    --shadow-offset-y: 2px;
    --letter-spacing: 0em;
    --spacing: 0.25rem;
    --shadow-2xs: 1px 2px 5px 1px hsl(0 0% 0% / 0.03);
    --shadow-xs: 1px 2px 5px 1px hsl(0 0% 0% / 0.03);
    --shadow-sm: 1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 1px 2px 0px hsl(0 0% 0% / 0.06);
    --shadow: 1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 1px 2px 0px hsl(0 0% 0% / 0.06);
    --shadow-md: 1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 2px 4px 0px hsl(0 0% 0% / 0.06);
    --shadow-lg: 1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 4px 6px 0px hsl(0 0% 0% / 0.06);
    --shadow-xl: 1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 8px 10px 0px hsl(0 0% 0% / 0.06);
    --shadow-2xl: 1px 2px 5px 1px hsl(0 0% 0% / 0.15);
}

/*
  ---break---
*/

@layer base {
  * {
    @apply border-border outline-ring/50;
    }
  body {
    @apply bg-background text-foreground;
    }
}